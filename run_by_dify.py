import json
import mimetypes
import os
import tempfile
import traceback

import requests
import pandas as pd
from tqdm import tqdm

# from dataset import education
from dataset2 import education
from dify_client import WorkflowClient

work_flow_client = WorkflowClient(api_key='app-DZt0scrz6m1In5IPxLbb3SvJ', base_url='http://*************/v1')

dify_user = 'MATERIAL_BUILD_TEST'


def upload_file(url):
    try:
        # Download the file from URL
        response = requests.get(url, stream=True)
        response.raise_for_status()

        # Get filename from URL
        filename = os.path.basename(url)  # Remove query parameters
        if not filename:
            filename = 'downloaded_file'

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            # Download file content
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
            temp_file_path = temp_file.name

            # Determine content type
            content_type = mimetypes.guess_type(url)[0] or 'application/octet-stream'

            # Upload the file
            with open(temp_file_path, "rb") as file_handle:
                files = {'file': (filename, file_handle, content_type)}
                upload_response = work_flow_client.file_upload(user=dify_user, files=files)

            return upload_response.json()
    finally:
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


def run(file_path):
    dify_file = upload_file(file_path)

    inputs = {
        "video": {
            "transfer_method": "local_file",
            "upload_file_id": dify_file.get('id'),
            "type": "video"
        }
    }

    response = work_flow_client.run(inputs=inputs, response_mode='blocking', user=dify_user)

    output_text = response.json().get('data', {}).get('outputs', {}).get('desc_json')

    return output_text


# for group in tqdm(education):
#     output_file = f'''{group['group']}-{group['cate']}.text'''
#     error_file = f'''{group['group']}-{group['cate']}.error'''
#
#     for material in tqdm(group['data']):
#         try:
#             result = run(material['url'])
#             with open(output_file, 'a') as f:
#                 f.write(f'''
# {material['id']}:
# {json.dumps(material)}
# {result}
#
# ''')
#         except Exception as e:
#             with open(error_file, 'a') as f:
#                 f.write(f'''
# {json.dumps(material)}
# {traceback.print_exc()}
#
#
# ''')


# 创建一个列表来存储所有数据行
data_rows = []

for cate, data in education.items():
    for sub_cate, materialUrls in data.items():
        for material in materialUrls:
            try:
                res = run(material)
                segment_list = json.loads(res)
                for segment in segment_list:
                    segmentNo = segment['segmentNo']
                    timeRange = segment['timeRange']
                    timeStamp = segment['timeStamp']
                    description = segment['description']
                    tags = segment['tags']

                    # 将数据添加到列表中
                    data_rows.append({
                        'cate': cate,
                        'sub_cate': sub_cate,
                        'material': material,
                        'segmentNo': segmentNo,
                        'timeRange': timeRange,
                        'timeStamp': timeStamp,
                        'description': description,
                        'tags': tags
                    })

                    print(f"处理完成: {cate} - {sub_cate} - 片段 {segmentNo}")
            except Exception as e:
                print(f"处理失败: {material}, 错误: {str(e)}")
                continue

# 将数据保存到Excel文件
if data_rows:
    df = pd.DataFrame(data_rows)
    excel_filename = 'material_segments_data.xlsx'
    df.to_excel(excel_filename, index=False, engine='openpyxl')
    print(f"数据已保存到 {excel_filename} 文件中，共 {len(data_rows)} 条记录")
else:
    print("没有数据可保存")