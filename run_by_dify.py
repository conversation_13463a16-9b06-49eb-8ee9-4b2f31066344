import json
import mimetypes
import os
import tempfile
import traceback

import requests
from tqdm import tqdm

# from dataset import education
from dataset2 import education
from dify_client import WorkflowClient

work_flow_client = WorkflowClient(api_key='app-DZt0scrz6m1In5IPxLbb3SvJ', base_url='http://*************/v1')

dify_user = 'MATERIAL_BUILD_TEST'


def upload_file(url):
    try:
        # Download the file from URL
        response = requests.get(url, stream=True)
        response.raise_for_status()

        # Get filename from URL
        filename = os.path.basename(url)  # Remove query parameters
        if not filename:
            filename = 'downloaded_file'

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            # Download file content
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
            temp_file_path = temp_file.name

            # Determine content type
            content_type = mimetypes.guess_type(url)[0] or 'application/octet-stream'

            # Upload the file
            with open(temp_file_path, "rb") as file_handle:
                files = {'file': (filename, file_handle, content_type)}
                upload_response = work_flow_client.file_upload(user=dify_user, files=files)

            return upload_response.json()
    finally:
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


def run(file_path):
    dify_file = upload_file(file_path)

    inputs = {
        "video": {
            "transfer_method": "local_file",
            "upload_file_id": dify_file.get('id'),
            "type": "video"
        }
    }

    response = work_flow_client.run(inputs=inputs, response_mode='blocking', user=dify_user)

    output_text = response.json().get('data', {}).get('outputs', {}).get('desc_json')

    return output_text


# for group in tqdm(education):
#     output_file = f'''{group['group']}-{group['cate']}.text'''
#     error_file = f'''{group['group']}-{group['cate']}.error'''
#
#     for material in tqdm(group['data']):
#         try:
#             result = run(material['url'])
#             with open(output_file, 'a') as f:
#                 f.write(f'''
# {material['id']}:
# {json.dumps(material)}
# {result}
#
# ''')
#         except Exception as e:
#             with open(error_file, 'a') as f:
#                 f.write(f'''
# {json.dumps(material)}
# {traceback.print_exc()}
#
#
# ''')


for cate, data in education.items():
    for sub_cate, materialUrls in data.items():
        for material in materialUrls:
            res = run(material)
            print(cate,sub_cate)
            print(json.loads(res))
            exit()
